import { ConfigDependentToolParams, IDE, Tool } from "..";
import { codebaseTool } from "./definitions/codebaseTool";
import { createNewFileTool } from "./definitions/createNewFile";
import { createRuleBlock } from "./definitions/createRuleBlock";
import { fetchUrlContentTool } from "./definitions/fetchUrlContent";
import { fileExistsTool } from "./definitions/fileExists";
import { getFileProblemsTool } from "./definitions/getFileProblems";
import { getProposedChangesTool } from "./definitions/getProposedChanges";
import { getTreeResultTool } from "./definitions/getTreeResult";
import { globSearchTool } from "./definitions/globSearch";
import { grepSearchTool } from "./definitions/grepSearch";
import { lsTool } from "./definitions/ls";
import { readCurrentlyOpenFileTool } from "./definitions/readCurrentlyOpenFile";
import { readFileTool } from "./definitions/readFile";
import { runTerminalCommandTool } from "./definitions/runTerminalCommand";
import { searchAndReplaceInFileTool } from "./definitions/searchAndReplaceInFile";
import { textEditorTool } from "./definitions/textEditor";
import { searchWebTool } from "./definitions/searchWeb";
import { viewDiffTool } from "./definitions/viewDiff";
import { viewRepoMapTool } from "./definitions/viewRepoMap";
import { viewSubdirectoryTool } from "./definitions/viewSubdirectory";

// I'm writing these as functions because we've messed up 3 TIMES by pushing to const, causing duplicate tool definitions on subsequent config loads.

// missing support for remote os calls: https://github.com/microsoft/vscode/issues/252269
const getLocalOnlyToolDefinitions = () => [toolDefinitions.grepSearchTool];

const getBaseToolDefinitions = () => [
  codebaseTool,
  // readFileTool,
  // createNewFileTool,
  // text editor tool (SWE-agent style)
  textEditorTool,
  runTerminalCommandTool,
  globSearchTool,
  // searchWebTool,
  viewDiffTool,
  readCurrentlyOpenFileTool,
  lsTool,
  getTreeResultTool,
  // createRuleBlock,
  fetchUrlContentTool,
  fileExistsTool,
  // getProposedChangesTool,
  // listDirTool,
];

export const getConfigDependentToolDefinitions = (
  params: ConfigDependentToolParams,
): Tool[] => [
  // requestRuleTool(params),
  // Search and replace is now generally available
  // searchAndReplaceInFileTool,
  // Keep edit file tool available for models that need it
  // editFileTool,
  ...(params.enableExperimentalTools
    ? [viewRepoMapTool, viewSubdirectoryTool]
    : []),
];

const getJetbrainsTools = () => {
  return [getFileProblemsTool];
};

const getXcodeTools = () => {
  return [];
};

export const getToolsForIde = async (ide: IDE): Promise<Tool[]> => {
  if (await ide.isWorkspaceRemote()) {
    return getBaseToolDefinitions();
  } else {
    const ideInfo = await ide.getIdeInfo();
    const baseTools = [
      ...getBaseToolDefinitions(),
      ...getLocalOnlyToolDefinitions(),
    ];

    switch (ideInfo.ideType) {
      case "jetbrains":
        return [...baseTools, ...getJetbrainsTools()];
      case "xcode":
        return [...baseTools, ...getXcodeTools()];
      default:
        return baseTools;
    }
  }
};
